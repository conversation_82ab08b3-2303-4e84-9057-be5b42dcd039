<!-- Rental Period Accordion Metabox -->
<div class="rental-period-accordion-wrapper card">
    <!-- Accordion Header -->
    <div class="card-header" id="rental-period-header">
        <button class="rental-period-toggle" type="button" aria-expanded="false" aria-controls="rental-period-content">
            <h4 class="card-title">{{ trans('plugins/real-estate::property.form.rental_period') }}</h4>
            <i class="fas fa-chevron-down rental-period-arrow"></i>
        </button>
    </div>

    <!-- Accordion Content -->
    <div class="card-body rental-period-content" id="rental-period-content" style="display: none;">
        <div class="rental-period-options">
            @if($rentalPeriodOptions && count($rentalPeriodOptions) > 0)
            <div class="position-relative form-check-group">
                @foreach($rentalPeriodOptions as $value => $label)
                 <label class="form-check form-check-inline" for="rental_period_{{ $value }}">
                    <input class="form-check-input select-search-full" id="rental_period_{{ $value }}" v-pre="1" type="radio" name="rental_period" value="{{ $value }}"  {{ $selectedRentalPeriod == $value ? 'checked' : '' }}>
                    <span class="form-check-label">{{ $label }}</span>
                </label>
                @endforeach
            </div>
            @else
                <p class="text-muted">{{ __('No rental period options available') }}</p>
            @endif
        </div>

      
    </div>
</div>

<style>
/* Rental Period Accordion Styles */
.rental-period-accordion-wrapper {
    margin-bottom: 1rem;
}


.rental-period-toggle {
    width: 100%;
    padding: 0 0 1rem 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.2s ease;
}

.rental-period-arrow {
    transition: transform 0.3s ease;
    color: #6c757d;
    font-size: 0.875rem;
}

.rental-period-toggle[aria-expanded="true"] .rental-period-arrow {
    transform: rotate(180deg);
}

.rental-period-content {
    transition: all 0.3s ease;
    overflow: hidden;
}
.rental-period-options .form-check-group{
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.rental-period-content.show {
    display: block !important;
}

.rental-period-options .form-check {
    width: calc(50% - 5px) !important;
    margin: 0;
}

/* Animation for smooth expand/collapse */
@keyframes slideDown {
    from {
        max-height: 0;
        opacity: 0;
    }
    to {
        max-height: 300px;
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        max-height: 300px;
        opacity: 1;
    }
    to {
        max-height: 0;
        opacity: 0;
    }
}

.rental-period-content.expanding {
    animation: slideDown 0.3s ease-out forwards;
}

.rental-period-content.collapsing {
    animation: slideUp 0.3s ease-out forwards;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize rental period accordion
    const toggle = document.querySelector('.rental-period-toggle');
    const content = document.querySelector('.rental-period-content');
    const arrow = document.querySelector('.rental-period-arrow');

    if (toggle && content && arrow) {
        // Set initial state (collapsed)
        content.style.display = 'none';
        toggle.setAttribute('aria-expanded', 'false');

        toggle.addEventListener('click', function(e) {
            e.preventDefault();

            const isExpanded = toggle.getAttribute('aria-expanded') === 'true';

            if (isExpanded) {
                // Collapse
                content.classList.remove('expanding');
                content.classList.add('collapsing');
                toggle.setAttribute('aria-expanded', 'false');

                setTimeout(() => {
                    content.style.display = 'none';
                    content.classList.remove('collapsing');
                }, 300);
            } else {
                // Expand
                content.style.display = 'block';
                content.classList.remove('collapsing');
                content.classList.add('expanding');
                toggle.setAttribute('aria-expanded', 'true');

                setTimeout(() => {
                    content.classList.remove('expanding');
                }, 300);
            }
        });
    }
});
</script>


