<?php

namespace Xmetr\RealEstate\Forms;

use Xmetr\Base\Forms\FieldOptions\ContentFieldOption;
use Xmetr\Base\Forms\FieldOptions\OnOffFieldOption;
use Xmetr\Base\Forms\FieldOptions\TextareaFieldOption;
use Xmetr\Base\Forms\Fields\OnOffField;
use Xmetr\Base\Forms\Fields\TextareaField;
use Xmetr\Base\Forms\FormFieldOptions;
use Xmetr\RealEstate\Facades\RealEstateHelper;
use Xmetr\RealEstate\Forms\Fields\CustomEditorField;
use Xmetr\RealEstate\Forms\Fields\MultipleUploadField;
use Xmetr\RealEstate\Forms\Fields\SingleVideoUploadField;
use Xmetr\RealEstate\Http\Requests\AccountPropertyRequest;
use Xmetr\RealEstate\Models\Property;
use Xmetr\RealEstate\Enums\RentalPeriodEnum;

class AccountPropertyForm extends PropertyForm
{
    public function setup(): void
    {
        parent::setup();



        $this
            ->model(Property::class)
            ->template('plugins/real-estate::account.forms.base')
            ->hasFiles()
            ->setValidatorClass(AccountPropertyRequest::class)
            ->remove('is_featured')
            ->remove('moderation_status')
            ->remove('content')
            ->remove('images[]')
            ->remove('never_expired')
            ->remove('rental_period')
            ->removeMetaBox('suitable')
            ->modify(
                'auto_renew',
                OnOffField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/real-estate::property.renew_notice', [
                        'days' => RealEstateHelper::propertyExpiredDays(),
                    ]))
                    ->defaultValue(false),
                true
            )
            ->remove('author_id')
            ->remove('name')
            ->addAfter(
                'type',
                'original_description',
                TextareaField::class, TextareaFieldOption::make()
                ->label(trans('plugins/real-estate::property.form.description'))
                ->maxLength(5000)
                ->placeholder(trans('plugins/real-estate::property.form.description_placeholder'))
                ->rows(4)
                ->required()
            )
            ->addAfter(
                'content',
                'images',
                MultipleUploadField::class,
                FormFieldOptions::make()
                    ->label(trans('plugins/real-estate::account-property.images', [
                        'max' => RealEstateHelper::maxPropertyImagesUploadByAgent(),
                    ]))
                    ->required()
            )
            ->addAfter(
                'images',
                'video',
                SingleVideoUploadField::class,
                FormFieldOptions::make()
                    ->label(trans('plugins/real-estate::account-property.video'))
            )
            ->addMetaBoxes([
                'rental_period_metabox' => [
                    'title' => '', // Empty title since we handle it in the template
                    'content' => view(
                        'plugins/real-estate::account.forms.rental-period-metabox',
                        [
                            'rentalPeriodOptions' => RentalPeriodEnum::labels(),
                            'selectedRentalPeriod' => $this->getModel()->rental_period ?? null,
                        ]
                    )->render(),
                    'priority' => 3,
                    'wrap' => false, // Don't wrap in card since we handle styling in template
                    'attributes' => [
                        'class' => 'rental-period-metabox',
                    ],
                ]
            ])
            ;
    }
}
