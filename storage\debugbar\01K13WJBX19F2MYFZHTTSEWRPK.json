{"__meta": {"id": "01K13WJBX19F2MYFZHTTSEWRPK", "datetime": "2025-07-26 17:30:24", "utime": **********.034663, "method": "GET", "uri": "/en/account/properties/create", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.104957, "end": **********.034686, "duration": 1.****************, "duration_str": "1.93s", "measures": [{"label": "Booting", "start": **********.104957, "relative_start": 0, "end": **********.11652, "relative_end": **********.11652, "duration": 1.****************, "duration_str": "1.01s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.116532, "relative_start": 1.****************, "end": **********.03469, "relative_end": 3.814697265625e-06, "duration": 0.****************, "duration_str": "918ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.135066, "relative_start": 1.**************, "end": **********.153886, "relative_end": **********.153886, "duration": 0.*****************, "duration_str": "18.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/real-estate::partials.form-features", "start": **********.2773, "relative_start": 1.****************, "end": **********.2773, "relative_end": **********.2773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.281652, "relative_start": 1.****************, "end": **********.281652, "relative_end": **********.281652, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.283746, "relative_start": 1.1787889003753662, "end": **********.283746, "relative_end": **********.283746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.285663, "relative_start": 1.1807057857513428, "end": **********.285663, "relative_end": **********.285663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.287613, "relative_start": 1.1826558113098145, "end": **********.287613, "relative_end": **********.287613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.289482, "relative_start": 1.1845250129699707, "end": **********.289482, "relative_end": **********.289482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.291397, "relative_start": 1.1864399909973145, "end": **********.291397, "relative_end": **********.291397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.293458, "relative_start": 1.1885008811950684, "end": **********.293458, "relative_end": **********.293458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.295625, "relative_start": 1.1906678676605225, "end": **********.295625, "relative_end": **********.295625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.297629, "relative_start": 1.1926720142364502, "end": **********.297629, "relative_end": **********.297629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.299713, "relative_start": 1.1947557926177979, "end": **********.299713, "relative_end": **********.299713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.301592, "relative_start": 1.1966350078582764, "end": **********.301592, "relative_end": **********.301592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.303479, "relative_start": 1.1985218524932861, "end": **********.303479, "relative_end": **********.303479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.305363, "relative_start": 1.2004058361053467, "end": **********.305363, "relative_end": **********.305363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.30791, "relative_start": 1.2029528617858887, "end": **********.30791, "relative_end": **********.30791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.30982, "relative_start": 1.2048628330230713, "end": **********.30982, "relative_end": **********.30982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.31172, "relative_start": 1.2067627906799316, "end": **********.31172, "relative_end": **********.31172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.313674, "relative_start": 1.208716869354248, "end": **********.313674, "relative_end": **********.313674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.315567, "relative_start": 1.2106099128723145, "end": **********.315567, "relative_end": **********.315567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.317467, "relative_start": 1.2125098705291748, "end": **********.317467, "relative_end": **********.317467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.319277, "relative_start": 1.2143199443817139, "end": **********.319277, "relative_end": **********.319277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.321135, "relative_start": 1.2161779403686523, "end": **********.321135, "relative_end": **********.321135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::partials.form-suitable", "start": **********.321955, "relative_start": 1.2169978618621826, "end": **********.321955, "relative_end": **********.321955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.322442, "relative_start": 1.217484951019287, "end": **********.322442, "relative_end": **********.322442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.323115, "relative_start": 1.2181580066680908, "end": **********.323115, "relative_end": **********.323115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.323737, "relative_start": 1.****************, "end": **********.323737, "relative_end": **********.323737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::account.forms.rental-period-metabox", "start": **********.335844, "relative_start": 1.****************, "end": **********.335844, "relative_end": **********.335844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.job-form-actions", "start": **********.340657, "relative_start": 1.****************, "end": **********.340657, "relative_end": **********.340657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.360372, "relative_start": 1.****************, "end": **********.360372, "relative_end": **********.360372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::account.forms.base", "start": **********.364905, "relative_start": 1.****************, "end": **********.364905, "relative_end": **********.364905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.form", "start": **********.438287, "relative_start": 1.****************, "end": **********.438287, "relative_end": **********.438287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language::partials.notification", "start": **********.442335, "relative_start": 1.****************, "end": **********.442335, "relative_end": **********.442335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::alert", "start": **********.444034, "relative_start": 1.*************094, "end": **********.444034, "relative_end": **********.444034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::920a4a8abfd99078c24a8f8db36a19f4", "start": **********.446825, "relative_start": 1.****************, "end": **********.446825, "relative_end": **********.446825, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::account.forms.fields.multiple-upload", "start": **********.44778, "relative_start": 1.***************, "end": **********.44778, "relative_end": **********.44778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.448494, "relative_start": 1.****************, "end": **********.448494, "relative_end": **********.448494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.448931, "relative_start": 1.****************, "end": **********.448931, "relative_end": **********.448931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.449332, "relative_start": 1.3443748950958252, "end": **********.449332, "relative_end": **********.449332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.450826, "relative_start": 1.****************, "end": **********.450826, "relative_end": **********.450826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::account.forms.fields.single-video-upload", "start": **********.451344, "relative_start": 1.****************, "end": **********.451344, "relative_end": **********.451344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.451849, "relative_start": 1.****************, "end": **********.451849, "relative_end": **********.451849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.452097, "relative_start": 1.***************, "end": **********.452097, "relative_end": **********.452097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.452297, "relative_start": 1.3473398685455322, "end": **********.452297, "relative_end": **********.452297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.452667, "relative_start": 1.3477098941802979, "end": **********.452667, "relative_end": **********.452667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.textarea", "start": **********.453173, "relative_start": 1.3482158184051514, "end": **********.453173, "relative_end": **********.453173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.453738, "relative_start": 1.348780870437622, "end": **********.453738, "relative_end": **********.453738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.454069, "relative_start": 1.349111795425415, "end": **********.454069, "relative_end": **********.454069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.454617, "relative_start": 1.3496599197387695, "end": **********.454617, "relative_end": **********.454617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.454829, "relative_start": 1.3498718738555908, "end": **********.454829, "relative_end": **********.454829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.455073, "relative_start": 1.3501160144805908, "end": **********.455073, "relative_end": **********.455073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": **********.563288, "relative_start": 1.4583308696746826, "end": **********.563288, "relative_end": **********.563288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.563858, "relative_start": 1.4589009284973145, "end": **********.563858, "relative_end": **********.563858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.564241, "relative_start": 1.4592838287353516, "end": **********.564241, "relative_end": **********.564241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.565042, "relative_start": 1.4600849151611328, "end": **********.565042, "relative_end": **********.565042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.565546, "relative_start": 1.4605889320373535, "end": **********.565546, "relative_end": **********.565546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.565811, "relative_start": 1.4608538150787354, "end": **********.565811, "relative_end": **********.565811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": **********.66054, "relative_start": 1.5555830001831055, "end": **********.66054, "relative_end": **********.66054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.661117, "relative_start": 1.5561599731445312, "end": **********.661117, "relative_end": **********.661117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.661408, "relative_start": 1.5564508438110352, "end": **********.661408, "relative_end": **********.661408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.661793, "relative_start": 1.5568358898162842, "end": **********.661793, "relative_end": **********.661793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.662279, "relative_start": 1.5573217868804932, "end": **********.662279, "relative_end": **********.662279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.662509, "relative_start": 1.5575518608093262, "end": **********.662509, "relative_end": **********.662509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": **********.662846, "relative_start": 1.5578889846801758, "end": **********.662846, "relative_end": **********.662846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.663236, "relative_start": 1.5582787990570068, "end": **********.663236, "relative_end": **********.663236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.66347, "relative_start": 1.5585129261016846, "end": **********.66347, "relative_end": **********.66347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.663728, "relative_start": 1.5587708950042725, "end": **********.663728, "relative_end": **********.663728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.664155, "relative_start": 1.5591979026794434, "end": **********.664155, "relative_end": **********.664155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.664396, "relative_start": 1.559438943862915, "end": **********.664396, "relative_end": **********.664396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": **********.665072, "relative_start": 1.560114860534668, "end": **********.665072, "relative_end": **********.665072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.665463, "relative_start": 1.5605058670043945, "end": **********.665463, "relative_end": **********.665463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.665719, "relative_start": 1.5607619285583496, "end": **********.665719, "relative_end": **********.665719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.666015, "relative_start": 1.5610578060150146, "end": **********.666015, "relative_end": **********.666015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.666458, "relative_start": 1.5615007877349854, "end": **********.666458, "relative_end": **********.666458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.666656, "relative_start": 1.5616989135742188, "end": **********.666656, "relative_end": **********.666656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.fields.google-map-autocomplete-field", "start": **********.667173, "relative_start": 1.562215805053711, "end": **********.667173, "relative_end": **********.667173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.667606, "relative_start": 1.5626490116119385, "end": **********.667606, "relative_end": **********.667606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.667856, "relative_start": 1.562898874282837, "end": **********.667856, "relative_end": **********.667856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.668284, "relative_start": 1.5633268356323242, "end": **********.668284, "relative_end": **********.668284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.668488, "relative_start": 1.5635309219360352, "end": **********.668488, "relative_end": **********.668488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.669249, "relative_start": 1.5642919540405273, "end": **********.669249, "relative_end": **********.669249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.66966, "relative_start": 1.5647029876708984, "end": **********.66966, "relative_end": **********.66966, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.670038, "relative_start": 1.5650808811187744, "end": **********.670038, "relative_end": **********.670038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.670465, "relative_start": 1.5655078887939453, "end": **********.670465, "relative_end": **********.670465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.67067, "relative_start": 1.5657129287719727, "end": **********.67067, "relative_end": **********.67067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.670897, "relative_start": 1.5659399032592773, "end": **********.670897, "relative_end": **********.670897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.671306, "relative_start": 1.5663487911224365, "end": **********.671306, "relative_end": **********.671306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.6717, "relative_start": 1.5667428970336914, "end": **********.6717, "relative_end": **********.6717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.671944, "relative_start": 1.5669867992401123, "end": **********.671944, "relative_end": **********.671944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.67237, "relative_start": 1.5674128532409668, "end": **********.67237, "relative_end": **********.67237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.672573, "relative_start": 1.5676159858703613, "end": **********.672573, "relative_end": **********.672573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.6728, "relative_start": 1.567842960357666, "end": **********.6728, "relative_end": **********.6728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.673081, "relative_start": 1.5681238174438477, "end": **********.673081, "relative_end": **********.673081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.673423, "relative_start": 1.5684659481048584, "end": **********.673423, "relative_end": **********.673423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.673669, "relative_start": 1.5687119960784912, "end": **********.673669, "relative_end": **********.673669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.674072, "relative_start": 1.5691149234771729, "end": **********.674072, "relative_end": **********.674072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.674276, "relative_start": 1.5693190097808838, "end": **********.674276, "relative_end": **********.674276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.674501, "relative_start": 1.5695438385009766, "end": **********.674501, "relative_end": **********.674501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.674847, "relative_start": 1.569889783859253, "end": **********.674847, "relative_end": **********.674847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.675145, "relative_start": 1.5701878070831299, "end": **********.675145, "relative_end": **********.675145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.675568, "relative_start": 1.5706110000610352, "end": **********.675568, "relative_end": **********.675568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.675789, "relative_start": 1.5708320140838623, "end": **********.675789, "relative_end": **********.675789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.676043, "relative_start": 1.5710859298706055, "end": **********.676043, "relative_end": **********.676043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.676328, "relative_start": 1.5713708400726318, "end": **********.676328, "relative_end": **********.676328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.676625, "relative_start": 1.5716679096221924, "end": **********.676625, "relative_end": **********.676625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.677082, "relative_start": 1.57212495803833, "end": **********.677082, "relative_end": **********.677082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.677284, "relative_start": 1.572326898574829, "end": **********.677284, "relative_end": **********.677284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.677518, "relative_start": 1.5725607872009277, "end": **********.677518, "relative_end": **********.677518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.677862, "relative_start": 1.5729048252105713, "end": **********.677862, "relative_end": **********.677862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.678215, "relative_start": 1.5732579231262207, "end": **********.678215, "relative_end": **********.678215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.678474, "relative_start": 1.573516845703125, "end": **********.678474, "relative_end": **********.678474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.679041, "relative_start": 1.5740838050842285, "end": **********.679041, "relative_end": **********.679041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.679254, "relative_start": 1.5742969512939453, "end": **********.679254, "relative_end": **********.679254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.679493, "relative_start": 1.574535846710205, "end": **********.679493, "relative_end": **********.679493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.679813, "relative_start": 1.5748558044433594, "end": **********.679813, "relative_end": **********.679813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.680164, "relative_start": 1.575206995010376, "end": **********.680164, "relative_end": **********.680164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.68051, "relative_start": 1.5755529403686523, "end": **********.68051, "relative_end": **********.68051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.680938, "relative_start": 1.5759809017181396, "end": **********.680938, "relative_end": **********.680938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.681162, "relative_start": 1.5762050151824951, "end": **********.681162, "relative_end": **********.681162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.681411, "relative_start": 1.5764539241790771, "end": **********.681411, "relative_end": **********.681411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.681721, "relative_start": 1.5767638683319092, "end": **********.681721, "relative_end": **********.681721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.682078, "relative_start": 1.5771207809448242, "end": **********.682078, "relative_end": **********.682078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.682337, "relative_start": 1.5773799419403076, "end": **********.682337, "relative_end": **********.682337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.682807, "relative_start": 1.5778498649597168, "end": **********.682807, "relative_end": **********.682807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.68303, "relative_start": 1.5780727863311768, "end": **********.68303, "relative_end": **********.68303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.683276, "relative_start": 1.5783188343048096, "end": **********.683276, "relative_end": **********.683276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.683572, "relative_start": 1.5786149501800537, "end": **********.683572, "relative_end": **********.683572, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.683918, "relative_start": 1.57896089553833, "end": **********.683918, "relative_end": **********.683918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.684176, "relative_start": 1.579218864440918, "end": **********.684176, "relative_end": **********.684176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.684601, "relative_start": 1.579643964767456, "end": **********.684601, "relative_end": **********.684601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.684823, "relative_start": 1.5798659324645996, "end": **********.684823, "relative_end": **********.684823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.685068, "relative_start": 1.580110788345337, "end": **********.685068, "relative_end": **********.685068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.685369, "relative_start": 1.5804119110107422, "end": **********.685369, "relative_end": **********.685369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.685723, "relative_start": 1.580765962600708, "end": **********.685723, "relative_end": **********.685723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.686196, "relative_start": 1.5812389850616455, "end": **********.686196, "relative_end": **********.686196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.686407, "relative_start": 1.5814499855041504, "end": **********.686407, "relative_end": **********.686407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.686626, "relative_start": 1.5816688537597656, "end": **********.686626, "relative_end": **********.686626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.6869, "relative_start": 1.5819427967071533, "end": **********.6869, "relative_end": **********.6869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.6872, "relative_start": 1.5822429656982422, "end": **********.6872, "relative_end": **********.6872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.68761, "relative_start": 1.5826528072357178, "end": **********.68761, "relative_end": **********.68761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.687798, "relative_start": 1.582840919494629, "end": **********.687798, "relative_end": **********.687798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.688011, "relative_start": 1.5830538272857666, "end": **********.688011, "relative_end": **********.688011, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.688281, "relative_start": 1.5833239555358887, "end": **********.688281, "relative_end": **********.688281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.688599, "relative_start": 1.5836420059204102, "end": **********.688599, "relative_end": **********.688599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.688828, "relative_start": 1.5838708877563477, "end": **********.688828, "relative_end": **********.688828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.689253, "relative_start": 1.5842959880828857, "end": **********.689253, "relative_end": **********.689253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.689444, "relative_start": 1.584486961364746, "end": **********.689444, "relative_end": **********.689444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.68967, "relative_start": 1.5847129821777344, "end": **********.68967, "relative_end": **********.68967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": **********.689956, "relative_start": 1.5849988460540771, "end": **********.689956, "relative_end": **********.689956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.690301, "relative_start": 1.585343837738037, "end": **********.690301, "relative_end": **********.690301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.690512, "relative_start": 1.585554838180542, "end": **********.690512, "relative_end": **********.690512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.690848, "relative_start": 1.5858910083770752, "end": **********.690848, "relative_end": **********.690848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.691275, "relative_start": 1.586317777633667, "end": **********.691275, "relative_end": **********.691275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.691468, "relative_start": 1.5865108966827393, "end": **********.691468, "relative_end": **********.691468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.691683, "relative_start": 1.5867259502410889, "end": **********.691683, "relative_end": **********.691683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.69196, "relative_start": 1.5870029926300049, "end": **********.69196, "relative_end": **********.69196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.692368, "relative_start": 1.5874109268188477, "end": **********.692368, "relative_end": **********.692368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.692617, "relative_start": 1.5876598358154297, "end": **********.692617, "relative_end": **********.692617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.69304, "relative_start": 1.5880827903747559, "end": **********.69304, "relative_end": **********.69304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.693249, "relative_start": 1.588291883468628, "end": **********.693249, "relative_end": **********.693249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.693483, "relative_start": 1.5885260105133057, "end": **********.693483, "relative_end": **********.693483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.69377, "relative_start": 1.5888128280639648, "end": **********.69377, "relative_end": **********.69377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.694107, "relative_start": 1.5891499519348145, "end": **********.694107, "relative_end": **********.694107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.694393, "relative_start": 1.5894358158111572, "end": **********.694393, "relative_end": **********.694393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.694792, "relative_start": 1.5898349285125732, "end": **********.694792, "relative_end": **********.694792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.695006, "relative_start": 1.5900487899780273, "end": **********.695006, "relative_end": **********.695006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.695281, "relative_start": 1.5903239250183105, "end": **********.695281, "relative_end": **********.695281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.695573, "relative_start": 1.59061598777771, "end": **********.695573, "relative_end": **********.695573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.695883, "relative_start": 1.590925931930542, "end": **********.695883, "relative_end": **********.695883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.696322, "relative_start": 1.591364860534668, "end": **********.696322, "relative_end": **********.696322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.696523, "relative_start": 1.5915658473968506, "end": **********.696523, "relative_end": **********.696523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.696755, "relative_start": 1.5917978286743164, "end": **********.696755, "relative_end": **********.696755, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off", "start": **********.697167, "relative_start": 1.592209815979004, "end": **********.697167, "relative_end": **********.697167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off", "start": **********.697683, "relative_start": 1.5927259922027588, "end": **********.697683, "relative_end": **********.697683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.toggle", "start": **********.698178, "relative_start": 1.5932209491729736, "end": **********.698178, "relative_end": **********.698178, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.698593, "relative_start": 1.5936357975006104, "end": **********.698593, "relative_end": **********.698593, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.698995, "relative_start": 1.5940380096435547, "end": **********.698995, "relative_end": **********.698995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.699207, "relative_start": 1.594249963760376, "end": **********.699207, "relative_end": **********.699207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.699422, "relative_start": 1.5944647789001465, "end": **********.699422, "relative_end": **********.699422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.6997, "relative_start": 1.594743013381958, "end": **********.6997, "relative_end": **********.6997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.70002, "relative_start": 1.5950629711151123, "end": **********.70002, "relative_end": **********.70002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.700295, "relative_start": 1.5953378677368164, "end": **********.700295, "relative_end": **********.700295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.700739, "relative_start": 1.5957818031311035, "end": **********.700739, "relative_end": **********.700739, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.700949, "relative_start": 1.595991849899292, "end": **********.700949, "relative_end": **********.700949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.701182, "relative_start": 1.5962247848510742, "end": **********.701182, "relative_end": **********.701182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.701473, "relative_start": 1.5965158939361572, "end": **********.701473, "relative_end": **********.701473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.701785, "relative_start": 1.5968279838562012, "end": **********.701785, "relative_end": **********.701785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.702203, "relative_start": 1.5972459316253662, "end": **********.702203, "relative_end": **********.702203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.702412, "relative_start": 1.5974547863006592, "end": **********.702412, "relative_end": **********.702412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.702646, "relative_start": 1.597688913345337, "end": **********.702646, "relative_end": **********.702646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.702929, "relative_start": 1.5979719161987305, "end": **********.702929, "relative_end": **********.702929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.703241, "relative_start": 1.5982840061187744, "end": **********.703241, "relative_end": **********.703241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.703658, "relative_start": 1.598701000213623, "end": **********.703658, "relative_end": **********.703658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.703868, "relative_start": 1.5989108085632324, "end": **********.703868, "relative_end": **********.703868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.7041, "relative_start": 1.5991427898406982, "end": **********.7041, "relative_end": **********.7041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off", "start": **********.704387, "relative_start": 1.5994298458099365, "end": **********.704387, "relative_end": **********.704387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off", "start": **********.704722, "relative_start": 1.5997648239135742, "end": **********.704722, "relative_end": **********.704722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.toggle", "start": **********.705045, "relative_start": 1.6000878810882568, "end": **********.705045, "relative_end": **********.705045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.705386, "relative_start": 1.600428819656372, "end": **********.705386, "relative_end": **********.705386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.705838, "relative_start": 1.6008808612823486, "end": **********.705838, "relative_end": **********.705838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.706048, "relative_start": 1.601090908050537, "end": **********.706048, "relative_end": **********.706048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.706311, "relative_start": 1.6013538837432861, "end": **********.706311, "relative_end": **********.706311, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off", "start": **********.706599, "relative_start": 1.6016418933868408, "end": **********.706599, "relative_end": **********.706599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off", "start": **********.706923, "relative_start": 1.6019659042358398, "end": **********.706923, "relative_end": **********.706923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.toggle", "start": **********.70723, "relative_start": 1.6022729873657227, "end": **********.70723, "relative_end": **********.70723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.707574, "relative_start": 1.602616786956787, "end": **********.707574, "relative_end": **********.707574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.708084, "relative_start": 1.6031270027160645, "end": **********.708084, "relative_end": **********.708084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.708293, "relative_start": 1.6033358573913574, "end": **********.708293, "relative_end": **********.708293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.708524, "relative_start": 1.6035668849945068, "end": **********.708524, "relative_end": **********.708524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off", "start": **********.708809, "relative_start": 1.6038517951965332, "end": **********.708809, "relative_end": **********.708809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off", "start": **********.709143, "relative_start": 1.6041858196258545, "end": **********.709143, "relative_end": **********.709143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.toggle", "start": **********.70946, "relative_start": 1.6045029163360596, "end": **********.70946, "relative_end": **********.70946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.709799, "relative_start": 1.604841947555542, "end": **********.709799, "relative_end": **********.709799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.710221, "relative_start": 1.6052639484405518, "end": **********.710221, "relative_end": **********.710221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.710428, "relative_start": 1.605470895767212, "end": **********.710428, "relative_end": **********.710428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.710662, "relative_start": 1.6057047843933105, "end": **********.710662, "relative_end": **********.710662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off", "start": **********.710947, "relative_start": 1.605989933013916, "end": **********.710947, "relative_end": **********.710947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off", "start": **********.71128, "relative_start": 1.606323003768921, "end": **********.71128, "relative_end": **********.71128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.toggle", "start": **********.711594, "relative_start": 1.6066370010375977, "end": **********.711594, "relative_end": **********.711594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.712087, "relative_start": 1.6071298122406006, "end": **********.712087, "relative_end": **********.712087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.712535, "relative_start": 1.6075778007507324, "end": **********.712535, "relative_end": **********.712535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.712779, "relative_start": 1.6078219413757324, "end": **********.712779, "relative_end": **********.712779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.713038, "relative_start": 1.6080808639526367, "end": **********.713038, "relative_end": **********.713038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.713354, "relative_start": 1.6083970069885254, "end": **********.713354, "relative_end": **********.713354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.713809, "relative_start": 1.6088519096374512, "end": **********.713809, "relative_end": **********.713809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.714298, "relative_start": 1.6093409061431885, "end": **********.714298, "relative_end": **********.714298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.714498, "relative_start": 1.6095409393310547, "end": **********.714498, "relative_end": **********.714498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.714724, "relative_start": 1.609766960144043, "end": **********.714724, "relative_end": **********.714724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.multi-check-list", "start": **********.715139, "relative_start": 1.6101818084716797, "end": **********.715139, "relative_end": **********.715139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.715575, "relative_start": 1.6106178760528564, "end": **********.715575, "relative_end": **********.715575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.multi-checklist", "start": **********.715905, "relative_start": 1.610947847366333, "end": **********.715905, "relative_end": **********.715905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.716748, "relative_start": 1.6117908954620361, "end": **********.716748, "relative_end": **********.716748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.717547, "relative_start": 1.6125898361206055, "end": **********.717547, "relative_end": **********.717547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.718223, "relative_start": 1.6132659912109375, "end": **********.718223, "relative_end": **********.718223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.718877, "relative_start": 1.613919973373413, "end": **********.718877, "relative_end": **********.718877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.719524, "relative_start": 1.6145668029785156, "end": **********.719524, "relative_end": **********.719524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.72017, "relative_start": 1.6152129173278809, "end": **********.72017, "relative_end": **********.72017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.fieldset", "start": **********.720821, "relative_start": 1.6158638000488281, "end": **********.720821, "relative_end": **********.720821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.721119, "relative_start": 1.616161823272705, "end": **********.721119, "relative_end": **********.721119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.721537, "relative_start": 1.6165800094604492, "end": **********.721537, "relative_end": **********.721537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.721731, "relative_start": 1.6167738437652588, "end": **********.721731, "relative_end": **********.721731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.721947, "relative_start": 1.6169898509979248, "end": **********.721947, "relative_end": **********.721947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.722221, "relative_start": 1.6172637939453125, "end": **********.722221, "relative_end": **********.722221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.722513, "relative_start": 1.617555856704712, "end": **********.722513, "relative_end": **********.722513, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.722912, "relative_start": 1.617954969406128, "end": **********.722912, "relative_end": **********.722912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.7231, "relative_start": 1.61814284324646, "end": **********.7231, "relative_end": **********.7231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.723439, "relative_start": 1.6184818744659424, "end": **********.723439, "relative_end": **********.723439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": **********.724092, "relative_start": 1.6191349029541016, "end": **********.724092, "relative_end": **********.724092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.724586, "relative_start": 1.61962890625, "end": **********.724586, "relative_end": **********.724586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.meta-box", "start": **********.725957, "relative_start": 1.620999813079834, "end": **********.725957, "relative_end": **********.725957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.727277, "relative_start": 1.6223199367523193, "end": **********.727277, "relative_end": **********.727277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.727806, "relative_start": 1.6228489875793457, "end": **********.727806, "relative_end": **********.727806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": **********.728188, "relative_start": 1.6232309341430664, "end": **********.728188, "relative_end": **********.728188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.728464, "relative_start": 1.623506784439087, "end": **********.728464, "relative_end": **********.728464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.meta-box", "start": **********.728794, "relative_start": 1.6238369941711426, "end": **********.728794, "relative_end": **********.728794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.form-content-only", "start": **********.733405, "relative_start": 1.6284480094909668, "end": **********.733405, "relative_end": **********.733405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.733829, "relative_start": 1.6288719177246094, "end": **********.733829, "relative_end": **********.733829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.734206, "relative_start": 1.629248857498169, "end": **********.734206, "relative_end": **********.734206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.734491, "relative_start": 1.6295340061187744, "end": **********.734491, "relative_end": **********.734491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.734983, "relative_start": 1.630025863647461, "end": **********.734983, "relative_end": **********.734983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.735222, "relative_start": 1.6302649974822998, "end": **********.735222, "relative_end": **********.735222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.735483, "relative_start": 1.630525827407837, "end": **********.735483, "relative_end": **********.735483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.textarea", "start": **********.735785, "relative_start": 1.6308279037475586, "end": **********.735785, "relative_end": **********.735785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.736184, "relative_start": 1.6312267780303955, "end": **********.736184, "relative_end": **********.736184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.736468, "relative_start": 1.6315109729766846, "end": **********.736468, "relative_end": **********.736468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.736925, "relative_start": 1.6319677829742432, "end": **********.736925, "relative_end": **********.736925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.73712, "relative_start": 1.6321628093719482, "end": **********.73712, "relative_end": **********.73712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.73734, "relative_start": 1.632382869720459, "end": **********.73734, "relative_end": **********.73734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.media-image", "start": **********.737711, "relative_start": 1.632753849029541, "end": **********.737711, "relative_end": **********.737711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.73812, "relative_start": 1.6331629753112793, "end": **********.73812, "relative_end": **********.73812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.image", "start": **********.738432, "relative_start": 1.6334748268127441, "end": **********.738432, "relative_end": **********.738432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.image", "start": **********.738911, "relative_start": 1.6339538097381592, "end": **********.738911, "relative_end": **********.738911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::image", "start": **********.741216, "relative_start": 1.636258840560913, "end": **********.741216, "relative_end": **********.741216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.742263, "relative_start": 1.6373059749603271, "end": **********.742263, "relative_end": **********.742263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c3e3c2b5f3306f2c083e21b9339fe63", "start": **********.744616, "relative_start": 1.6396589279174805, "end": **********.744616, "relative_end": **********.744616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.745005, "relative_start": 1.6400477886199951, "end": **********.745005, "relative_end": **********.745005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.745595, "relative_start": 1.6406378746032715, "end": **********.745595, "relative_end": **********.745595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.745833, "relative_start": 1.6408758163452148, "end": **********.745833, "relative_end": **********.745833, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.746152, "relative_start": 1.6411948204040527, "end": **********.746152, "relative_end": **********.746152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-radio", "start": **********.746946, "relative_start": 1.64198899269104, "end": **********.746946, "relative_end": **********.746946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.750201, "relative_start": 1.6452438831329346, "end": **********.750201, "relative_end": **********.750201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-radio", "start": **********.751575, "relative_start": 1.6466178894042969, "end": **********.751575, "relative_end": **********.751575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.radio", "start": **********.752367, "relative_start": 1.6474099159240723, "end": **********.752367, "relative_end": **********.752367, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.radio", "start": **********.75299, "relative_start": 1.6480329036712646, "end": **********.75299, "relative_end": **********.75299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.753371, "relative_start": 1.648413896560669, "end": **********.753371, "relative_end": **********.753371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.753883, "relative_start": 1.64892578125, "end": **********.753883, "relative_end": **********.753883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.754113, "relative_start": 1.649155855178833, "end": **********.754113, "relative_end": **********.754113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.754382, "relative_start": 1.6494247913360596, "end": **********.754382, "relative_end": **********.754382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/seo-helper::meta-box", "start": **********.755793, "relative_start": 1.****************, "end": **********.755793, "relative_end": **********.755793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.actions", "start": **********.756921, "relative_start": 1.****************, "end": **********.756921, "relative_end": **********.756921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::hr", "start": **********.758203, "relative_start": 1.****************, "end": **********.758203, "relative_end": **********.758203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box-wrap", "start": **********.758885, "relative_start": 1.****************, "end": **********.758885, "relative_end": **********.758885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.759815, "relative_start": 1.654857873916626, "end": **********.759815, "relative_end": **********.759815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.760157, "relative_start": 1.****************, "end": **********.760157, "relative_end": **********.760157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": **********.760408, "relative_start": 1.****************, "end": **********.760408, "relative_end": **********.760408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.760637, "relative_start": 1.****************, "end": **********.760637, "relative_end": **********.760637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": **********.761208, "relative_start": 1.****************, "end": **********.761208, "relative_end": **********.761208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": **********.762499, "relative_start": 1.****************, "end": **********.762499, "relative_end": **********.762499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": **********.763301, "relative_start": 1.65834379196167, "end": **********.763301, "relative_end": **********.763301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.769606, "relative_start": 1.6646490097045898, "end": **********.769606, "relative_end": **********.769606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::themes.dashboard.layouts.master", "start": **********.770637, "relative_start": 1.665679931640625, "end": **********.770637, "relative_end": **********.770637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::themes.dashboard.layouts.header", "start": **********.891338, "relative_start": 1.7863810062408447, "end": **********.891338, "relative_end": **********.891338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::themes.dashboard.layouts.header-meta", "start": **********.894257, "relative_start": 1.7892999649047852, "end": **********.894257, "relative_end": **********.894257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::header", "start": **********.900432, "relative_start": 1.7954750061035156, "end": **********.900432, "relative_end": **********.900432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.header", "start": **********.904751, "relative_start": 1.7997939586639404, "end": **********.904751, "relative_end": **********.904751, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": **********.91871, "relative_start": 1.8137528896331787, "end": **********.91871, "relative_end": **********.91871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": **********.920562, "relative_start": 1.8156049251556396, "end": **********.920562, "relative_end": **********.920562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::themes.dashboard.layouts.body", "start": **********.009026, "relative_start": 1.904068946838379, "end": **********.009026, "relative_end": **********.009026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::themes.dashboard.layouts.footer", "start": **********.009898, "relative_start": 1.9049408435821533, "end": **********.009898, "relative_end": **********.009898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::footer", "start": **********.011438, "relative_start": 1.9064807891845703, "end": **********.011438, "relative_end": **********.011438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::fronts.toast-notification", "start": **********.023556, "relative_start": 1.9185988903045654, "end": **********.023556, "relative_end": **********.023556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.029792, "relative_start": 1.924834966659546, "end": **********.031172, "relative_end": **********.031172, "duration": 0.0013799667358398438, "duration_str": "1.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 65004960, "peak_usage_str": "62MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 310, "nb_templates": 310, "templates": [{"name": "1x plugins/real-estate::partials.form-features", "param_count": null, "params": [], "start": **********.27727, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/partials/form-features.blade.phpplugins/real-estate::partials.form-features", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fpartials%2Fform-features.blade.php:1", "ajax": false, "filename": "form-features.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/real-estate::partials.form-features"}, {"name": "30x ********************************::form.checkbox", "param_count": null, "params": [], "start": **********.281628, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 30, "name_original": "********************************::form.checkbox"}, {"name": "1x plugins/real-estate::partials.form-suitable", "param_count": null, "params": [], "start": **********.321934, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/partials/form-suitable.blade.phpplugins/real-estate::partials.form-suitable", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fpartials%2Fform-suitable.blade.php:1", "ajax": false, "filename": "form-suitable.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/real-estate::partials.form-suitable"}, {"name": "1x plugins/real-estate::account.forms.rental-period-metabox", "param_count": null, "params": [], "start": **********.33582, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/account/forms/rental-period-metabox.blade.phpplugins/real-estate::account.forms.rental-period-metabox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Faccount%2Fforms%2Frental-period-metabox.blade.php:1", "ajax": false, "filename": "rental-period-metabox.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/real-estate::account.forms.rental-period-metabox"}, {"name": "1x theme.xmetr::partials.job-form-actions", "param_count": null, "params": [], "start": **********.340636, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/job-form-actions.blade.phptheme.xmetr::partials.job-form-actions", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Fjob-form-actions.blade.php:1", "ajax": false, "filename": "job-form-actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.job-form-actions"}, {"name": "2x core/js-validation::bootstrap", "param_count": null, "params": [], "start": **********.360346, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/js-validation/resources/views/bootstrap.blade.phpcore/js-validation::bootstrap", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fjs-validation%2Fresources%2Fviews%2Fbootstrap.blade.php:1", "ajax": false, "filename": "bootstrap.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/js-validation::bootstrap"}, {"name": "1x plugins/real-estate::account.forms.base", "param_count": null, "params": [], "start": **********.364882, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/account/forms/base.blade.phpplugins/real-estate::account.forms.base", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Faccount%2Fforms%2Fbase.blade.php:1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/real-estate::account.forms.base"}, {"name": "1x core/base::forms.form", "param_count": null, "params": [], "start": **********.438261, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/form.blade.phpcore/base::forms.form", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform.blade.php:1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.form"}, {"name": "1x plugins/language::partials.notification", "param_count": null, "params": [], "start": **********.44231, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/language/resources/views/partials/notification.blade.phpplugins/language::partials.notification", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fresources%2Fviews%2Fpartials%2Fnotification.blade.php:1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/language::partials.notification"}, {"name": "1x ********************************::alert", "param_count": null, "params": [], "start": **********.444011, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/alert.blade.php********************************::alert", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Falert.blade.php:1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::alert"}, {"name": "1x __components::920a4a8abfd99078c24a8f8db36a19f4", "param_count": null, "params": [], "start": **********.446801, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/920a4a8abfd99078c24a8f8db36a19f4.blade.php__components::920a4a8abfd99078c24a8f8db36a19f4", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F920a4a8abfd99078c24a8f8db36a19f4.blade.php:1", "ajax": false, "filename": "920a4a8abfd99078c24a8f8db36a19f4.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::920a4a8abfd99078c24a8f8db36a19f4"}, {"name": "1x plugins/real-estate::account.forms.fields.multiple-upload", "param_count": null, "params": [], "start": **********.447757, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/account/forms/fields/multiple-upload.blade.phpplugins/real-estate::account.forms.fields.multiple-upload", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Faccount%2Fforms%2Ffields%2Fmultiple-upload.blade.php:1", "ajax": false, "filename": "multiple-upload.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/real-estate::account.forms.fields.multiple-upload"}, {"name": "24x core/base::forms.partials.label", "param_count": null, "params": [], "start": **********.448473, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/label.blade.phpcore/base::forms.partials.label", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Flabel.blade.php:1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 24, "name_original": "core/base::forms.partials.label"}, {"name": "39x core/base::forms.partials.help-block", "param_count": null, "params": [], "start": **********.44891, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/help-block.blade.phpcore/base::forms.partials.help-block", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fhelp-block.blade.php:1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 39, "name_original": "core/base::forms.partials.help-block"}, {"name": "39x core/base::forms.partials.errors", "param_count": null, "params": [], "start": **********.449312, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/errors.blade.phpcore/base::forms.partials.errors", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferrors.blade.php:1", "ajax": false, "filename": "errors.blade.php", "line": "?"}, "render_count": 39, "name_original": "core/base::forms.partials.errors"}, {"name": "35x core/base::forms.columns.column-span", "param_count": null, "params": [], "start": **********.450804, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/columns/column-span.blade.phpcore/base::forms.columns.column-span", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fcolumn-span.blade.php:1", "ajax": false, "filename": "column-span.blade.php", "line": "?"}, "render_count": 35, "name_original": "core/base::forms.columns.column-span"}, {"name": "1x plugins/real-estate::account.forms.fields.single-video-upload", "param_count": null, "params": [], "start": **********.451322, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/account/forms/fields/single-video-upload.blade.phpplugins/real-estate::account.forms.fields.single-video-upload", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Faccount%2Fforms%2Ffields%2Fsingle-video-upload.blade.php:1", "ajax": false, "filename": "single-video-upload.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/real-estate::account.forms.fields.single-video-upload"}, {"name": "2x core/base::forms.fields.textarea", "param_count": null, "params": [], "start": **********.453153, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/textarea.blade.phpcore/base::forms.fields.textarea", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftextarea.blade.php:1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.fields.textarea"}, {"name": "37x ********************************::form.field", "param_count": null, "params": [], "start": **********.454049, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/field.blade.php********************************::form.field", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffield.blade.php:1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 37, "name_original": "********************************::form.field"}, {"name": "5x core/base::forms.fields.custom-select", "param_count": null, "params": [], "start": **********.563262, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/custom-select.blade.phpcore/base::forms.fields.custom-select", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fcustom-select.blade.php:1", "ajax": false, "filename": "custom-select.blade.php", "line": "?"}, "render_count": 5, "name_original": "core/base::forms.fields.custom-select"}, {"name": "5x core/base::forms.partials.custom-select", "param_count": null, "params": [], "start": **********.564219, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/custom-select.blade.phpcore/base::forms.partials.custom-select", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fcustom-select.blade.php:1", "ajax": false, "filename": "custom-select.blade.php", "line": "?"}, "render_count": 5, "name_original": "core/base::forms.partials.custom-select"}, {"name": "1x theme.xmetr::partials.fields.google-map-autocomplete-field", "param_count": null, "params": [], "start": **********.667144, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/fields/google-map-autocomplete-field.blade.phptheme.xmetr::partials.fields.google-map-autocomplete-field", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Ffields%2Fgoogle-map-autocomplete-field.blade.php:1", "ajax": false, "filename": "google-map-autocomplete-field.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.fields.google-map-autocomplete-field"}, {"name": "10x core/base::forms.fields.html", "param_count": null, "params": [], "start": **********.669642, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/html.blade.phpcore/base::forms.fields.html", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fhtml.blade.php:1", "ajax": false, "filename": "html.blade.php", "line": "?"}, "render_count": 10, "name_original": "core/base::forms.fields.html"}, {"name": "11x core/base::forms.fields.text", "param_count": null, "params": [], "start": **********.671287, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/text.blade.phpcore/base::forms.fields.text", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftext.blade.php:1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 11, "name_original": "core/base::forms.fields.text"}, {"name": "5x core/base::forms.fields.on-off", "param_count": null, "params": [], "start": **********.697147, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/on-off.blade.phpcore/base::forms.fields.on-off", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fon-off.blade.php:1", "ajax": false, "filename": "on-off.blade.php", "line": "?"}, "render_count": 5, "name_original": "core/base::forms.fields.on-off"}, {"name": "5x core/base::forms.partials.on-off", "param_count": null, "params": [], "start": **********.697663, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/on-off.blade.phpcore/base::forms.partials.on-off", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fon-off.blade.php:1", "ajax": false, "filename": "on-off.blade.php", "line": "?"}, "render_count": 5, "name_original": "core/base::forms.partials.on-off"}, {"name": "5x ********************************::form.toggle", "param_count": null, "params": [], "start": **********.698158, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/toggle.blade.php********************************::form.toggle", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ftoggle.blade.php:1", "ajax": false, "filename": "toggle.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::form.toggle"}, {"name": "1x core/base::forms.fields.multi-check-list", "param_count": null, "params": [], "start": **********.715118, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/multi-check-list.blade.phpcore/base::forms.fields.multi-check-list", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fmulti-check-list.blade.php:1", "ajax": false, "filename": "multi-check-list.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.multi-check-list"}, {"name": "1x core/base::forms.partials.multi-checklist", "param_count": null, "params": [], "start": **********.715885, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/multi-checklist.blade.phpcore/base::forms.partials.multi-checklist", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fmulti-checklist.blade.php:1", "ajax": false, "filename": "multi-checklist.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.multi-checklist"}, {"name": "1x ********************************::form.fieldset", "param_count": null, "params": [], "start": **********.720801, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/fieldset.blade.php********************************::form.fieldset", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffieldset.blade.php:1", "ajax": false, "filename": "fieldset.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.fieldset"}, {"name": "3x ********************************::card.body.index", "param_count": null, "params": [], "start": **********.724055, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/body/index.blade.php********************************::card.body.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fbody%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::card.body.index"}, {"name": "3x ********************************::card.index", "param_count": null, "params": [], "start": **********.724543, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/index.blade.php********************************::card.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::card.index"}, {"name": "2x core/base::forms.partials.meta-box", "param_count": null, "params": [], "start": **********.725922, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/meta-box.blade.phpcore/base::forms.partials.meta-box", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fmeta-box.blade.php:1", "ajax": false, "filename": "meta-box.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.partials.meta-box"}, {"name": "2x ********************************::card.title", "param_count": null, "params": [], "start": **********.727254, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/title.blade.php********************************::card.title", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Ftitle.blade.php:1", "ajax": false, "filename": "title.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::card.title"}, {"name": "2x ********************************::card.header.index", "param_count": null, "params": [], "start": **********.727784, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/header/index.blade.php********************************::card.header.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fheader%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::card.header.index"}, {"name": "1x core/base::forms.form-content-only", "param_count": null, "params": [], "start": **********.733384, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/form-content-only.blade.phpcore/base::forms.form-content-only", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform-content-only.blade.php:1", "ajax": false, "filename": "form-content-only.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.form-content-only"}, {"name": "1x core/base::forms.fields.media-image", "param_count": null, "params": [], "start": **********.737691, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/media-image.blade.phpcore/base::forms.fields.media-image", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fmedia-image.blade.php:1", "ajax": false, "filename": "media-image.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.media-image"}, {"name": "1x core/base::forms.partials.image", "param_count": null, "params": [], "start": **********.738412, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/image.blade.phpcore/base::forms.partials.image", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fimage.blade.php:1", "ajax": false, "filename": "image.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.image"}, {"name": "1x ********************************::form.image", "param_count": null, "params": [], "start": **********.738891, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/image.blade.php********************************::form.image", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fimage.blade.php:1", "ajax": false, "filename": "image.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.image"}, {"name": "1x ********************************::image", "param_count": null, "params": [], "start": **********.741165, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/image.blade.php********************************::image", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fimage.blade.php:1", "ajax": false, "filename": "image.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::image"}, {"name": "1x ********************************::button", "param_count": null, "params": [], "start": **********.742229, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/button.blade.php********************************::button", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::button"}, {"name": "1x __components::1c3e3c2b5f3306f2c083e21b9339fe63", "param_count": null, "params": [], "start": **********.744592, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/1c3e3c2b5f3306f2c083e21b9339fe63.blade.php__components::1c3e3c2b5f3306f2c083e21b9339fe63", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F1c3e3c2b5f3306f2c083e21b9339fe63.blade.php:1", "ajax": false, "filename": "1c3e3c2b5f3306f2c083e21b9339fe63.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1c3e3c2b5f3306f2c083e21b9339fe63"}, {"name": "1x core/base::forms.fields.custom-radio", "param_count": null, "params": [], "start": **********.746907, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/custom-radio.blade.phpcore/base::forms.fields.custom-radio", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fcustom-radio.blade.php:1", "ajax": false, "filename": "custom-radio.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.custom-radio"}, {"name": "1x core/base::forms.partials.custom-radio", "param_count": null, "params": [], "start": **********.751548, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/custom-radio.blade.phpcore/base::forms.partials.custom-radio", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fcustom-radio.blade.php:1", "ajax": false, "filename": "custom-radio.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.custom-radio"}, {"name": "2x ********************************::form.radio", "param_count": null, "params": [], "start": **********.752344, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/radio.blade.php********************************::form.radio", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fradio.blade.php:1", "ajax": false, "filename": "radio.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::form.radio"}, {"name": "1x packages/seo-helper::meta-box", "param_count": null, "params": [], "start": **********.755752, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/seo-helper/resources/views/meta-box.blade.phppackages/seo-helper::meta-box", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fseo-helper%2Fresources%2Fviews%2Fmeta-box.blade.php:1", "ajax": false, "filename": "meta-box.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/seo-helper::meta-box"}, {"name": "1x ********************************::card.actions", "param_count": null, "params": [], "start": **********.756895, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/actions.blade.php********************************::card.actions", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Factions.blade.php:1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::card.actions"}, {"name": "1x ********************************::hr", "param_count": null, "params": [], "start": **********.758177, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/hr.blade.php********************************::hr", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fhr.blade.php:1", "ajax": false, "filename": "hr.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::hr"}, {"name": "1x core/base::elements.meta-box-wrap", "param_count": null, "params": [], "start": **********.758861, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/elements/meta-box-wrap.blade.phpcore/base::elements.meta-box-wrap", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fmeta-box-wrap.blade.php:1", "ajax": false, "filename": "meta-box-wrap.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::elements.meta-box-wrap"}, {"name": "3x core/base::elements.meta-box", "param_count": null, "params": [], "start": **********.761173, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/elements/meta-box.blade.phpcore/base::elements.meta-box", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fmeta-box.blade.php:1", "ajax": false, "filename": "meta-box.blade.php", "line": "?"}, "render_count": 3, "name_original": "core/base::elements.meta-box"}, {"name": "1x plugins/real-estate::themes.dashboard.layouts.master", "param_count": null, "params": [], "start": **********.770608, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/themes/dashboard/layouts/master.blade.phpplugins/real-estate::themes.dashboard.layouts.master", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fthemes%2Fdashboard%2Flayouts%2Fmaster.blade.php:1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/real-estate::themes.dashboard.layouts.master"}, {"name": "1x plugins/real-estate::themes.dashboard.layouts.header", "param_count": null, "params": [], "start": **********.891314, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/themes/dashboard/layouts/header.blade.phpplugins/real-estate::themes.dashboard.layouts.header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fthemes%2Fdashboard%2Flayouts%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/real-estate::themes.dashboard.layouts.header"}, {"name": "1x plugins/real-estate::themes.dashboard.layouts.header-meta", "param_count": null, "params": [], "start": **********.894237, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/themes/dashboard/layouts/header-meta.blade.phpplugins/real-estate::themes.dashboard.layouts.header-meta", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fthemes%2Fdashboard%2Flayouts%2Fheader-meta.blade.php:1", "ajax": false, "filename": "header-meta.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/real-estate::themes.dashboard.layouts.header-meta"}, {"name": "1x assets::header", "param_count": null, "params": [], "start": **********.900407, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\packages\\laravel-assets\\src\\Providers/../../resources/views/header.blade.phpassets::header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Flaravel-assets%2Fresources%2Fviews%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::header"}, {"name": "1x theme.xmetr::partials.header", "param_count": null, "params": [], "start": **********.90473, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/header.blade.phptheme.xmetr::partials.header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.header"}, {"name": "2x plugins/social-login::login-options", "param_count": null, "params": [], "start": **********.918689, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/social-login/resources/views/login-options.blade.phpplugins/social-login::login-options", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Fsocial-login%2Fresources%2Fviews%2Flogin-options.blade.php:1", "ajax": false, "filename": "login-options.blade.php", "line": "?"}, "render_count": 2, "name_original": "plugins/social-login::login-options"}, {"name": "1x plugins/real-estate::themes.dashboard.layouts.body", "param_count": null, "params": [], "start": **********.008981, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/themes/dashboard/layouts/body.blade.phpplugins/real-estate::themes.dashboard.layouts.body", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fthemes%2Fdashboard%2Flayouts%2Fbody.blade.php:1", "ajax": false, "filename": "body.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/real-estate::themes.dashboard.layouts.body"}, {"name": "1x plugins/real-estate::themes.dashboard.layouts.footer", "param_count": null, "params": [], "start": **********.009863, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/themes/dashboard/layouts/footer.blade.phpplugins/real-estate::themes.dashboard.layouts.footer", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fthemes%2Fdashboard%2Flayouts%2Ffooter.blade.php:1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/real-estate::themes.dashboard.layouts.footer"}, {"name": "1x assets::footer", "param_count": null, "params": [], "start": **********.011401, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\packages\\laravel-assets\\src\\Providers/../../resources/views/footer.blade.phpassets::footer", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Flaravel-assets%2Fresources%2Fviews%2Ffooter.blade.php:1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::footer"}, {"name": "1x packages/theme::fronts.toast-notification", "param_count": null, "params": [], "start": **********.023512, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/theme/resources/views/fronts/toast-notification.blade.phppackages/theme::fronts.toast-notification", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Ffronts%2Ftoast-notification.blade.php:1", "ajax": false, "filename": "toast-notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::fronts.toast-notification"}]}, "queries": {"count": 15, "nb_statements": 15, "nb_visible_statements": 15, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00685, "accumulated_duration_str": "6.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `re_accounts` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "account", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Middleware\\RedirectIfNotAccount.php", "line": 16}], "start": **********.16708, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 6.131}, {"sql": "select * from `slugs_translations` where `lang_code` = 'en' and `key` = 'properties' limit 1", "type": "query", "params": [], "bindings": ["en", "properties"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 98}, {"index": 15, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.171094, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "RedirectIncorrectLanguagePrefix.php:98", "source": {"index": 14, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 98}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fapp%2FHttp%2FMiddleware%2FRedirectIncorrectLanguagePrefix.php:98", "ajax": false, "filename": "RedirectIncorrectLanguagePrefix.php", "line": "98"}, "connection": "xmetr", "explain": null, "start_percent": 6.131, "width_percent": 4.818}, {"sql": "select * from `slugs` where `key` = 'properties' limit 1", "type": "query", "params": [], "bindings": ["properties"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 107}, {"index": 15, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.172486, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "RedirectIncorrectLanguagePrefix.php:107", "source": {"index": 14, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 107}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fapp%2FHttp%2FMiddleware%2FRedirectIncorrectLanguagePrefix.php:107", "ajax": false, "filename": "RedirectIncorrectLanguagePrefix.php", "line": "107"}, "connection": "xmetr", "explain": null, "start_percent": 10.949, "width_percent": 4.234}, {"sql": "select `name`, `id` from `re_projects` order by `created_at` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 71}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/AccountPropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\AccountPropertyForm.php", "line": 23}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 19, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}], "start": **********.179374, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 15.182, "width_percent": 5.839}, {"sql": "select `title`, `id` from `re_currencies`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 75}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/AccountPropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\AccountPropertyForm.php", "line": 23}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 17, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}], "start": **********.186992, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "PropertyForm.php:75", "source": {"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 75}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FForms%2FPropertyForm.php:75", "ajax": false, "filename": "PropertyForm.php", "line": "75"}, "connection": "xmetr", "explain": null, "start_percent": 21.022, "width_percent": 6.131}, {"sql": "select `id`, `name` from `re_features`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 99}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/AccountPropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\AccountPropertyForm.php", "line": 23}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 19, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}], "start": **********.206362, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 27.153, "width_percent": 5.547}, {"sql": "select `id`, `name` from `re_facilities`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 123}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/AccountPropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\AccountPropertyForm.php", "line": 23}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 19, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}], "start": **********.2276678, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 32.701, "width_percent": 6.131}, {"sql": "select `id`, `name`, `parent_id` from `re_categories` where `status` = 'published'", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 126}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/helpers/helpers.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\helpers\\helpers.php", "line": 38}, {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/AccountPropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\AccountPropertyForm.php", "line": 23}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}], "start": **********.328048, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 38.832, "width_percent": 7.007}, {"sql": "select `lang_name` from `languages` where `lang_code` = 'en_US' limit 1", "type": "query", "params": [], "bindings": ["en_US"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Providers\\HookServiceProvider.php", "line": 163}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.439543, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 45.839, "width_percent": 6.277}, {"sql": "select `name`, `id` from `countries` order by `order` asc, `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/location/src/Fields/SelectLocationField.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Fields\\SelectLocationField.php", "line": 77}, {"index": 17, "namespace": null, "name": "platform/plugins/location/src/Fields/SelectLocationField.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Fields\\SelectLocationField.php", "line": 255}, {"index": 18, "namespace": "view", "name": "core/base::forms.form", "file": "D:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/form.blade.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.455623, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 52.117, "width_percent": 8.613}, {"sql": "select `name`, `id` from `countries`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/location/src/Fields/SelectLocationField.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Fields\\SelectLocationField.php", "line": 113}, {"index": 17, "namespace": null, "name": "platform/plugins/location/src/Fields/SelectLocationField.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Fields\\SelectLocationField.php", "line": 260}, {"index": 18, "namespace": "view", "name": "core/base::forms.form", "file": "D:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/form.blade.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.566184, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 60.73, "width_percent": 8.321}, {"sql": "select `name`, `id` from `states` where `country_id` = 22 order by `order` asc, `name` asc", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/location/src/Fields/SelectLocationField.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Fields\\SelectLocationField.php", "line": 129}, {"index": 17, "namespace": null, "name": "platform/plugins/location/src/Fields/SelectLocationField.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Fields\\SelectLocationField.php", "line": 260}, {"index": 18, "namespace": "view", "name": "core/base::forms.form", "file": "D:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/form.blade.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.657171, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 69.051, "width_percent": 6.569}, {"sql": "select * from `media_files` where `media_files`.`id` = 178 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [178], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Account.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Account.php", "line": 175}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": "view", "name": "theme.xmetr::partials.header", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/header.blade.php", "line": 58}], "start": **********.914965, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 75.62, "width_percent": 8.029}, {"sql": "select * from `re_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/helpers/currencies.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\helpers\\currencies.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.922607, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 83.65, "width_percent": 7.153}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/packages/theme/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Providers\\HookServiceProvider.php", "line": 468}], "start": **********.017301, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 90.803, "width_percent": 9.197}]}, "models": {"data": {"Xmetr\\Location\\Models\\Country": {"value": 336, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCountry.php:1", "ajax": false, "filename": "Country.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Currency": {"value": 86, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Facility": {"value": 23, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FFacility.php:1", "ajax": false, "filename": "Facility.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Feature": {"value": 21, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FFeature.php:1", "ajax": false, "filename": "Feature.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Project": {"value": 8, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProject.php:1", "ajax": false, "filename": "Project.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Category": {"value": 3, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCategory.php:1", "ajax": false, "filename": "Category.php", "line": "?"}}, "Xmetr\\Location\\Models\\State": {"value": 3, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FState.php:1", "ajax": false, "filename": "State.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Account": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FAccount.php:1", "ajax": false, "filename": "Account.php", "line": "?"}}, "Xmetr\\Language\\Models\\Language": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Xmetr\\Media\\Models\\MediaFile": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFile.php:1", "ajax": false, "filename": "MediaFile.php", "line": "?"}}, "Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 484, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://xmetr.gc/en/account/properties/create", "action_name": "public.account.properties.create", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\AccountPropertyController@create", "uri": "GET en/account/properties/create", "controller": "Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\AccountPropertyController@create<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FFronts%2FAccountPropertyController.php:80\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Xmetr\\RealEstate\\Http\\Controllers\\Fronts", "prefix": "en/account/properties", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FFronts%2FAccountPropertyController.php:80\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php:80-89</a>", "middleware": "web, core, localeSessionRedirect, localizationRedirect, account, Xmetr\\RealEstate\\Http\\Middleware\\EnsureAccountIsApproved", "duration": "1.93s", "peak_memory": "66MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"90 characters\">https://xmetr.gc/en/properties/long-term-rent-of-a-3-room-apartment-70m2-in-paris-france-2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1548 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; wishlist=657; project_wishlist=25; _ga_KQ6X76DET4=GS2.1.s1753545705$o107$g1$t1753550571$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IlpFWDBqYnBwdG1IK1dFczBKWE1iOWc9PSIsInZhbHVlIjoiNm9qMTl3LzFoc0lIbjRKYjAvTTlWTzZKR2tPRzAxUmMxZDE2M1NVaG5PQVlUaGZKVmpuU1FJQ0IwWE5ySERUWEp3aDc4bFhrRGpCU25GNFFtVmNrVjdRYXpzR3lvN0JsRmFTUnZWdXV4Zm1UUXFnbEl0SmphY29UbkpHZXZRaEEiLCJtYWMiOiJkYzRiZWE5NWUyMzQzMzI1YjA5NTUwMmVjNzcxNzVlYTAyY2RlNzBiZjM3NTZlM2VlZmZkMDI5Mjg1MzUxN2Q5IiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6IndTSHhpT01vSVpTV1ZrbHBCcjhwa2c9PSIsInZhbHVlIjoiT1lPZFlITEFyY240RDJxTnozQlA1eEc2TU5OUHBldE5xUVB3QkNhanJFNEVwR2lydjRKbGxqa01KWjVhalpRTWpmUGFxaDhETHFYaE9QSVJNQnpNSlhtMlRHV25HNmVGeUdkblBpL1AvTUU0SHdXOVdmVS9YMCtZek50OHR2Y2EiLCJtYWMiOiJiNzUyOWQ5ZDgxNjg3YmMxZjU0NzMzZjI2OTI5OTVkN2RlOGJiODYzNmE0Mjk2MTY3MTU2YzdjMDYwNmM4MmM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>project_wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6HXj4kO8Ajm1LU4XOruTwqNCwBTw8y5aWqdRmGMM</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">J6KlajMTQR9MsRDqe3w8eUJYVHSwE6lYDCR1b5cF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-607226628 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 26 Jul 2025 17:30:24 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-607226628\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6HXj4kO8Ajm1LU4XOruTwqNCwBTw8y5aWqdRmGMM</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">https://xmetr.gc/en/account/properties/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>viewed_property</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1172</span> => <span class=sf-dump-num>**********</span>\n    <span class=sf-dump-key>1171</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_property_daily</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1172</span> => <span class=sf-dump-num>**********</span>\n    <span class=sf-dump-key>1171</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_account_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://xmetr.gc/en/account/properties/create", "action_name": "public.account.properties.create", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\AccountPropertyController@create"}, "badge": null}}